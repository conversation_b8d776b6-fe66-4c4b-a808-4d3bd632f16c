import React, { useState, useEffect, useMemo, useRef } from "react";
import { Button, message, Input, InputRef } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { ColumnsType } from "antd/es/table";
import axios from "axios";
import { SearchOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../../../apiCalls";
import { handleApiError } from "../../../../utils/ApiErrorHandler";
import ErrorFallback from "../../../Error/ErrorPage";
import dayjs from "dayjs";
import {
  CreativeFileObject,
  CreativeFileResponseType,
} from "../../../../types/CreativeFileType/CreativeFileType";
import FilterButtons from "../../../shared/FilterButton/FilterButton";
import { useTableFilters } from "../../../../hooks/useTableFilter";
import CommonPagination from "../../../shared/Pagination/commonPagination";
import DataTable from "../../../shared/DataTable/commonDataTable";
import FilterMenu from "../../../shared/FilterMenu";
import useMetaData from "../../../../hooks/useMetaData";
import StatusToggle from "../../../UI/ToggleStatus";
import showConfirmActionModal from "../../../UI/PopUpModal";

const CreativeFileList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const { metaData, errors } = useMetaData();
  const [data, setData] = useState<CreativeFileObject[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [searchValue, setSearchValue] = useState<string>("");
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const navigate = useNavigate();

  const codeInputRef = useRef<InputRef>(null);

  const [dynamicAttributes, setDynamicAttributes] = useState({
    creative_types: [] as string[], // Ensure it's always an array
  });

  // const abortControllerRef = useRef<AbortController | null>(null);
  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const creatives = metaData?.creative_meta;
    if (creatives) {
      setDynamicAttributes(creatives);
    }
  }, [metaData, errors]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchCreatives = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<CreativeFileResponseType>(
          "cms/menu/creatives-list/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setData(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchCreatives();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  // const handleStatusChange = (creative_id: number, isActive: boolean) => {
  //   Modal.confirm({
  //     title: isActive ? "Activate Type" : "Deactivate Type",
  //     content: isActive
  //       ? "Are you sure you want to activate this type?"
  //       : "Are you sure you want to deactivate this type?",
  //     okText: "Yes",
  //     cancelText: "No",
  //     className: "custom-modal", // Apply custom class
  //     okButtonProps: { className: "custom-modal-ok-button" },
  //     cancelButtonProps: { className: "custom-modal-cancel-button" },
  //     onOk: async () => {
  //       try {
  //         setLoading(true);
  //         const response = await axiosInstance.put(
  //           `/cms/menu/creatives-details/${creative_id}/`,
  //           {
  //             is_active: isActive,
  //           }
  //         );

  //         if (response.status === 200) {
  //           message.success(`Creative File Status ${response.data.message}`);
  //           setData((prevData) =>
  //             prevData.map((creative) =>
  //               creative.id === creative_id
  //                 ? { ...creative, is_active: isActive }
  //                 : creative
  //             )
  //           );
  //         } else {
  //           message.error("Failed to update user status.");
  //         }
  //       } catch (err: unknown) {
  //         if (axios.isAxiosError(err)) {
  //           message.error(
  //             err.response?.data?.message ||
  //               "An error occurred while updating user status."
  //           );
  //         } else {
  //           message.error("An unexpected error occurred.");
  //         }
  //       } finally {
  //         setLoading(false);
  //       }
  //     },
  //   });
  // };

  const handleStatusChange = (creative_id: number, isActive: boolean) => {
    // console.log("Creative ID:", creative_id);
    // console.log("Is Active:", isActive);
    const confirmUpdate = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.put(
          `/cms/menu/creatives-details/${creative_id}/`,
          {
            is_active: isActive,
          }
        );

        if (response.status === 200) {
          message.success(`Creative File Status ${response.data.message}`);
          setData((prevData) =>
            prevData.map((creative) =>
              creative.id === creative_id
                ? { ...creative, is_active: isActive }
                : creative
            )
          );
        } else {
          message.error("Failed to update user status.");
        }
      } catch (err: unknown) {
        if (axios.isAxiosError(err)) {
          message.error(
            err.response?.data?.message ||
              "An error occurred while updating user status."
          );
        } else {
          message.error("An unexpected error occurred.");
        }
      } finally {
        setLoading(false);
      }
    };

    showConfirmActionModal({
      isActive,
      onConfirm: confirmUpdate,
      entityName: "Creative File",
    });
  };

  const handleSearchChange = (value: string) => {
    handleFilterChange("name", value);
  };

  // const getFilterMenu = (
  //   setSelectedKeys: (keys: string[]) => void,
  //   selectedKeys: React.Key[],
  //   confirm: () => void,
  //   filterKey: string,
  //   options: { label: string; value: string | number }[]
  // ) => {
  //   console.log("Filter options:", options); // Log before mapping

  //   return (
  //     <Menu
  //       onClick={({ key }) => {
  //         const stringKey = String(key);
  //         setSelectedKeys(stringKey ? [stringKey] : []);
  //         confirm();
  //         handleFilterChange(filterKey, stringKey);
  //       }}
  //       selectedKeys={selectedKeys.map((key) => String(key))}
  //       items={[
  //         { key: "All", label: "All" }, // Static "All" option
  //         ...options.map((option) => ({
  //           key: String(option.value),
  //           label: option.label,
  //         })),
  //       ]}
  //     />
  //   );
  // };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "name") {
      setSearchValue("");
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    setSearchValue("");
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const formatText = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const columns: ColumnsType<CreativeFileObject> = useMemo(
    () => [
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        render: (code: string, record: CreativeFileObject) => (
          <Link
            className="common-link text-decoration-none"
            to={`/creative-file-details/${record.id}`}
          >
            {formatText(code)}
          </Link>
        ),
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: "15%",
        filteredValue: filters.type ? [filters.type] : null,
        filterDropdown: (props) => (
          <FilterMenu
            {...props}
            filterKey="type"
            options={dynamicAttributes.creative_types.map((status) => ({
              label: formatText(status),
              value: status,
            }))}
            handleFilterChange={handleFilterChange}
          />
        ),
        render: (value: string) => formatText(value),
      },
      {
        title: "Content Type",
        dataIndex: "content_type",
        key: "content_type",
      },
      // {
      //   title: "Created At",
      //   dataIndex: "created_at",
      //   key: "created_at",
      //   render: (created: string) =>
      //     dayjs(created).format("DD MMM YYYY, hh:mm A"),
      // },
      {
        title: "Updated At",
        dataIndex: "updated_at",
        key: "updated_at",
        render: (updated: string) =>
          dayjs(updated).format("DD MMM YYYY, hh:mm A"),
      },
      {
        title: "Status",
        dataIndex: "is_active",
        key: "status",
        width: "10%",
        fixed: "right" as "right",
        render: (is_active: boolean, record: CreativeFileObject) => (
          <div className="d-flex">
            <StatusToggle
              isActive={is_active}
              id={record.id}
              onToggle={() => handleStatusChange(record.id, !record.is_active)}
            />
          </div>
        ),
      },
    ],
    [handleStatusChange]
  );

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Creative List</div>
        <Button
          type="primary"
          className="add-store"
          onClick={() => navigate(`/add-creative-files`)}
        >
          Add New Creative File
        </Button>
      </div>
      <div className="d-flex justify-content-between align-items-center mb-3 mt-4 flex-wrap">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <Input.Search
              ref={codeInputRef}
              placeholder="Search by Name"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={(value) => {
                if (value.trim()) {
                  handleSearchChange(value);
                }
              }}
              onFocus={handleFocus}
              onBlur={handleBlur} // Keep icon if text exists
              prefix={
                <SearchOutlined
                  style={{ visibility: isFocused ? "visible" : "hidden" }}
                />
              }
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearAllFilters={clearAllFiltersHandler}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatText}
              filters={filters}
            />
          </div>
        </div>
      </div>

      <div className="pt-2 mt-2">
        <DataTable<CreativeFileObject>
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={false}
          loading={loading}
          // scroll={{ x: 1000 }}
        />
      </div>
      <CommonPagination
        current={currentPage}
        pageSize={pageSize}
        total={totalCount}
        showSizeChanger
        onShowSizeChange={handlePageChange}
        onChange={handlePageChange}
      />
    </div>
  );
};

export default CreativeFileList;
