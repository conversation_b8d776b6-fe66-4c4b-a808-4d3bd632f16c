import React, {
  forwardRef,
  useCallback,
  useState,
  useImperativeHandle,
} from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

interface SearchInputProps {
  value: string;
  onChange: (val: string) => void;
  onSearch: (val: string) => void;
  placeholder?: string;
}

const SearchInput = forwardRef<HTMLInputElement | null, SearchInputProps>(
  ({ value, onChange, onSearch, placeholder = "Search..." }, ref) => {
    const [isFocused, setIsFocused] = useState(false);

    const handleFocus = useCallback(() => setIsFocused(true), []);
    const handleBlur = useCallback(() => setIsFocused(false), []);

    // Forward the ref to the input element
    useImperativeHandle(ref, () => inputRef.current, []);

    const inputRef = React.useRef<HTMLInputElement | null>(null);

    return (
      <Input.Search
        ref={inputRef}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onSearch={(val) => {
          if (val.trim()) onSearch(val);
        }}
        onFocus={handleFocus}
        onBlur={handleBlur}
        prefix={
          <SearchOutlined
            style={{ visibility: isFocused || value ? "visible" : "hidden" }}
          />
        }
        allowClear
      />
    );
  }
);

export default React.memo(SearchInput);
